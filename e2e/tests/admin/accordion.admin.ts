import { test as base, expect } from '@playwright/test'
import { addPage, deletePage } from '../utils/page'
import {
    appAccordionSummaryTestId,
    contentEditorLeftColumnTestId,
    contentEditorMainTestId,
    defaultStructureTitle,
    defaultTemplateTitle,
    expandCollapseAllAccordionsButtonTestId,
    expandCollapseAllAccordionsMenuTestId,
    formRendererTestId
} from '../../constants'
import { signInTenant1Admin } from '../utils/auth-utils'

const test = base.extend({
    page: async ({ page }, use) => {
        await signInTenant1Admin(page)
        const pageTitle = 'E2E Page' + Math.random().toFixed(5)
        await addPage(page, {
            title: pageTitle,
            templateName: defaultTemplateTitle,
            structureName: defaultStructureTitle
        })
        await expect(page.getByTestId('save-button').first()).toBeVisible()
        await page.waitForTimeout(2000)
        await use(page)
        await deletePage(page, { title: pageTitle })
    }
})

test('can be individually opened and closed', async ({ page }) => {
    test.setTimeout(65000)
    const allAccordions = await page.getByTestId(contentEditorMainTestId).getByTestId(appAccordionSummaryTestId).all()
    expect(allAccordions.length).toBeTruthy()

    for (const accordion of allAccordions) {
        expect(accordion).toHaveAttribute('aria-expanded', 'true')
        accordion.click()
        await page.waitForTimeout(500)
        expect(accordion).toHaveAttribute('aria-expanded', 'false')
    }

})

test('can expand all and collapse all', async ({ page }) => {
    test.setTimeout(65000)
    await page.getByTestId(expandCollapseAllAccordionsButtonTestId).first().click()
    const menu = page.getByTestId(expandCollapseAllAccordionsMenuTestId)
    expect(menu).toBeVisible()
    await menu.getByText('Expand All').click()
    await page.waitForTimeout(400)
    expect(menu).not.toBeVisible()

    const allAccordionsLocator = page.getByTestId(contentEditorMainTestId).getByTestId(appAccordionSummaryTestId).all()
    for (const accordion of await allAccordionsLocator) {
        expect(accordion).toHaveAttribute('aria-expanded', 'true')
    }

    await page.getByTestId(expandCollapseAllAccordionsButtonTestId).first().click()
    expect(menu).toBeVisible()
    await menu.getByText('Collapse All').click()
    await page.waitForTimeout(400)
    expect(menu).not.toBeVisible()

    for (const accordion of await allAccordionsLocator) {
        expect(accordion).toHaveAttribute('aria-expanded', 'false')
    }
})

// tests revision history accordion
test('can expand and collapse uncontrolled accordion', async ({ page }) => {
    test.setTimeout(65000)
    const revisionHistoryLocator = page.getByTestId(contentEditorLeftColumnTestId).first().getByTestId(appAccordionSummaryTestId).filter({ hasText: 'Revision History' })
    expect(revisionHistoryLocator).toHaveAttribute('aria-expanded', 'false')
    await revisionHistoryLocator.click()
    expect(revisionHistoryLocator).toHaveAttribute('aria-expanded', 'true')
    await revisionHistoryLocator.click()
    expect(revisionHistoryLocator).toHaveAttribute('aria-expanded', 'false')
})
