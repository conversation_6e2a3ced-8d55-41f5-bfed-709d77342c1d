# Workspaces

#### TODO to resolve: `TODO: @Anatoly`

## TBD
- [?] Templates' paths. The current `path` approach for each row is not very efficient and should be refactored.

## Overview
Users can manage content across multiple workspace environments (similar to git branches),
so that they can prepare and review content changes before publishing them to the public-facing site.

1. For version 1, we will only support two workspaces: `live` and `draft`.
2. "Deletion" works differently for `live` and non-`live` workspaces:
   - `live`: always soft-deleted. When content in `live` is deleted, other workspaces are not affected.
   - non-`live`: deleted completely.
3. Only `live` can be branched from.

## Implementation details

1. Content entities will implement a Workspaced interface with:

    ```go
    package shared

    type (
        Workspaced interface {
            GetWorkspace() string
            GetEntityID() uuid.UUID
            SetWorkspace(workspace string)
        }
    )
    ```

   - `EffectiveIn` is an array of strings that exists only on `"live"` entities.
   - The `EffectiveIn` field is used for query optimization with the pattern `workspace = ? OR ? = ANY(effective_in)`.
     - os if an entity is not present in the requested workspace, the system will retrieve the `live` version instead.
   - The `EffectiveIn` field is updated when an entity is deleted or created in a specific workspace. This is currently implemented using a database trigger. 
