package admin2

import (
	"contentmanager/infrastructure/database/pgxx"
	"contentmanager/library/shared"
	"contentmanager/library/shared/pagination"
	"contentmanager/library/shared/pagx"
	"contentmanager/library/shared/result"
	"contentmanager/library/shared/sortx"
	"contentmanager/pkgs/content"
	"contentmanager/pkgs/content/admin"
	uuid "github.com/satori/go.uuid"
	"strings"
)

type (
	SearchQuery struct {
		pagx.Query
		sortx.SortingQuery
		Search       string
		ContentTypes []admin.Type `json:"ContentTypes[]"`
		Tags         []uuid.UUID  `json:"Tags[]"`
		Departments  []uuid.UUID  `json:"Departments[]"`
		Sites        []uuid.UUID  `json:"Sites[]"`
		SiteID       *uuid.UUID   `binding:"omitempty,cm_uuid"`
		StructureID  *uuid.UUID
		IDs          []uuid.UUID `json:"IDs[]"`
		Inactive     bool
		Status       string
		SiteOnly     bool
		Workspace    string
		Editors      []uuid.UUID `json:"Editors[]"`
		Templates    []uuid.UUID `json:"Templates[]"`
		UsePriority  bool
	}
)

func Search(r *shared.AppContext, params SearchQuery) result.Result[pagx.Paginated[content.Content]] {
	if len(params.Workspace) == 0 {
		params.Workspace = "live"
	}

	var pag pagx.Paginated[content.Content]
	dbQuery := r.TenantDatabase().Omit("Structure", "Content").
		Where(pgxx.Workspace(params.Workspace, "content."))

	dbQuery = dbQuery.Where("content.active = ?", !params.Inactive)

	if len(params.Status) > 0 {
		dbQuery = dbQuery.Where(pgxx.Status(r.AppTime().NowUTC(), "content.", params.Status))
	}

	if len(params.IDs) > 0 {
		dbQuery = dbQuery.Where("content.id IN (?)", params.IDs)

		// If we have IDs, we ignore sites and siteID
	} else if len(params.Sites) > 0 {
		dbQuery = dbQuery.Where(pgxx.ArrayHasAny("content.sites", params.Sites))
	}

	if params.SiteOnly {
		if params.SiteID != nil && len(params.Sites) == 0 {
			dbQuery = dbQuery.Where(pgxx.ArrayHasAny("content.sites", []uuid.UUID{*params.SiteID}))
		}
		dbQuery = dbQuery.Where("array_length(content.sites, 1) = 1")
	}

	if len(params.Search) > 0 {
		subqueries, rest := pgxx.ExtractCommands(params.Search)
		dbQuery = dbQuery.Where("( content.title || ' ' || content.id) ilike ( ? )", "%"+rest+"%")
		for _, subquery := range subqueries {
			if subquery.Column == "title" {
				dbQuery = dbQuery.Where("content.title ilike ?", "%"+subquery.Keyword+"%")
			} else if subquery.Column == "route" {
				dbQuery = dbQuery.Where("content.route ilike ?", "%"+subquery.Keyword+"%")
			} else if strings.HasPrefix(subquery.Column, "settings.") {
				dbQuery = dbQuery.Where("content.settings->>? ilike ?", strings.TrimPrefix(subquery.Column, "settings."), "%"+subquery.Keyword+"%")
			}
		}
	}

	if len(params.Departments) > 0 {
		dbQuery = dbQuery.Where("content.department_id IN (?)", params.Departments)
	}

	if len(params.ContentTypes) > 0 {
		dbQuery = dbQuery.
			Where(" content.type IN (?)", params.ContentTypes)
	}

	if params.StructureID != nil {
		dbQuery = dbQuery.Where("content.structure_id = ?", params.StructureID)
	}

	if len(params.Tags) > 0 {
		dbQuery = dbQuery.Where(pgxx.ArrayHasAny("content.tags", params.Tags))
	}

	if len(params.Editors) > 0 {
		dbQuery = dbQuery.Where("(content.owner in (?) OR content.publisher in (?))", params.Editors, params.Editors)
	}

	if len(params.Templates) > 0 {
		dbQuery = dbQuery.Where("subpath(content.path, -2, 1)::text::uuid in (?)", params.Templates)
	}

	if params.UsePriority {
		dbQuery = dbQuery.
			Where(" (content.settings->>'priority') IS NOT NULL")
	}

	dbQuery = dbQuery.Order(params.GetSortingSQL("updated"))

	return result.Check(pag, pagination.Paginate(dbQuery, params.Query, &pag))
}
