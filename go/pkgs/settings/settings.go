package settings

import (
	driver2 "contentmanager/infrastructure/database/driver"
	"contentmanager/infrastructure/database/pgxx"
	"contentmanager/library/shared"
	"contentmanager/library/shared/errx"
	"contentmanager/library/shared/pagination"
	"contentmanager/library/shared/pagx"
	"contentmanager/library/shared/result"
	"contentmanager/library/shared/sortx"
	commonModels "contentmanager/library/tenant/common/models"
	"contentmanager/library/utils/slicexx"
	"contentmanager/pkgs/auth/permissions"
	"contentmanager/pkgs/config"
	"contentmanager/pkgs/googleapi"
	"contentmanager/pkgs/msgraph"
	"contentmanager/servers/importer/edsby/edsby_api"
	"contentmanager/servers/importer/facebook/facebook_api"
	"contentmanager/servers/importer/google_calendar/google_calendar_api"
	"encoding/json"
	"errors"
	"fmt"
	uuid "github.com/satori/go.uuid"
	"gorm.io/gorm"
	"regexp"
)

type (
	SettingsType string
)

const (
	SiteSettingsCustom     SettingsType = "site-settings-custom"
	Edsby                  SettingsType = "edsby"
	EdsbySecrets           SettingsType = "edsby-secrets"
	Facebook               SettingsType = "facebook"
	FacebookSecrets        SettingsType = "facebook-secrets"
	Outlook                SettingsType = "outlook"
	OutlookSecrets         SettingsType = "outlook-secrets"
	AzureDirectorySecrets  SettingsType = "azure-directory-secrets"
	GoogleCalendar         SettingsType = "google-calendar"
	GoogleCalendarSecrets  SettingsType = "google-calendar-secrets"
	GoogleWorkspaceSecrets SettingsType = "google-workspace-secrets"
	Instagram              SettingsType = "instagram"
	ICS                    SettingsType = "ics"
	MenuItems              SettingsType = "menu-items"
)

type (
	// Settings is the model for the DB settings table.
	Settings struct {
		ID uuid.UUID `gorm:"column:id;type:uuid; primary_key; not null; default:uuid_generate_v4();"`
		commonModels.Tracking
		Name        string // required, has to be unique and case-insensitive unique(lower(name), site_id)
		Description string
		Type        SettingsType
		Sites       driver2.PgUUIDArray `gorm:"column:sites;type:uuid[];default:NULL"`
		Data        json.RawMessage
		Public      bool
		Active      bool
	}

	SearchQuery struct {
		pagx.Query
		sortx.SortingQuery
		Search       string
		Type         []SettingsType `json:"Type[]"`
		AdminID      *uuid.UUID     `binding:"omitempty,cm_uuid"`
		Sites        *[]uuid.UUID   `json:"Sites[]"`
		IgnoreSiteID *bool
		Active       *bool
		Public       *bool
	}

	By struct {
		Name   *string
		Type   *SettingsType
		SiteID *uuid.UUID
	}

	SettingsDTO struct {
		Name        string // required, has to be unique and case-insensitive
		Description string
		Type        SettingsType
		Sites       driver2.PgUUIDArray
		Data        json.RawMessage
		Public      bool
	}
)

func (Settings) TableName() string {
	return "settings"
}

func (s Settings) Validate() error {
	ee := map[string]string{}

	if len(s.Name) == 0 {
		ee["Name"] = "Name is required. "
	} else if matched, err := regexp.MatchString(`^[-_a-zA-Z0-9]+$`, s.Name); !matched || err != nil {
		ee["Name"] = "Name can only contain letters, numbers, dashes and underscores. "
	}
	if len(s.Type) == 0 {
		ee["Type"] = "Type is required. "
	}
	if len(s.Data) == 0 {
		ee["Data"] = "Data is required. "
	}

	if len(ee) > 0 {
		return errx.NewValidationError(ee)
	}
	return nil
}

func (Settings) SearchQuery() string {
	return `(settings.id || ' ' || settings.type || ' ' || settings.name || ' ' || coalesce(settings.description, ' ')) ilike ( ? )`
}

func Search(r *shared.AppContext, q SearchQuery) result.Result[pagx.Paginated[Settings]] {
	var pag pagx.Paginated[Settings]

	tx := r.TenantDatabase()

	if q.IgnoreSiteID == nil || *q.IgnoreSiteID == false {
		if q.Sites != nil && len(*q.Sites) > 0 {
			tx = tx.Where(pgxx.ArrayHasAny("sites", *q.Sites))
		} else {
			tx = tx.Where("sites IS NULL")
		}
	}

	if q.Active != nil {
		tx = tx.Where("active = ?", q.Active)
	} else {
		// Show only active settings by default
		tx = tx.Where("active = ?", true)
	}

	if q.Public != nil {
		tx = tx.Where("public = ?", q.Public)
	}

	if q.AdminID != nil {
		tx = tx.Where("created_by = ? OR updated_by = ?", q.AdminID, q.AdminID)
	}

	if len(q.Type) > 0 {
		tx = tx.Where("type in ?", q.Type)
	}

	if len(q.Search) > 0 {
		tx = tx.Where(Settings{}.SearchQuery(), `%`+q.Search+`%`)
	}

	tx = tx.Order(q.GetSortingSQL())

	return result.Check(pag, pagination.Paginate(tx, q.Query, &pag))
}

func GetSettingById(r *shared.AppContext, id uuid.UUID) result.Result[Settings] {
	var setting Settings
	if err := r.TenantDatabase().Where("active = ?", true).First(&setting, "id = ?", id).Error; err != nil {
		return result.Error(err, Settings{})
	}

	return result.Success(setting)
}

func Create(r *shared.AppContext, s SettingsDTO) result.Result[uuid.UUID] {
	setting := Settings{
		Name:        s.Name,
		Description: s.Description,
		Type:        s.Type,
		Sites:       s.Sites,
		Public:      s.Public,
		Active:      true,
	}

	var err error
	if setting.Data, err = preprocessSettingByType(r.TenantDatabase(), s.Data, s.Type); err != nil {
		return result.Error(err, uuid.Nil)
	}
	if err := setting.Validate(); err != nil {
		return result.Error(err, uuid.Nil)
	}
	if err := ValidateNameUniqueAcrossSites(r.TenantDatabase(), setting); err != nil {
		return result.Error(err, uuid.Nil)
	}

	setting.Track(r.AppTime().NowUTC(), r.Account().ID)

	return result.Check(setting.ID, r.TenantDatabase().Create(&setting).Error)
}

func Update(r *shared.AppContext, id uuid.UUID, s SettingsDTO) result.EmptyResult {
	var setting Settings
	if err := r.TenantDatabase().First(&setting, "id = ?", id).Error; err != nil {
		return result.ErrorEmpty(err)
	}

	var err error
	if setting.Data, err = preprocessSettingByType(r.TenantDatabase(), s.Data, s.Type); err != nil {
		return result.ErrorEmpty(err)
	}
	setting.Name = s.Name
	setting.Description = s.Description
	setting.Type = s.Type
	setting.Sites = s.Sites
	setting.Public = s.Public

	if err := setting.Validate(); err != nil {
		return result.ErrorEmpty(err)
	}
	if err := ValidateNameUniqueAcrossSites(r.TenantDatabase(), setting); err != nil {
		return result.ErrorEmpty(err)
	}

	setting.Track(r.AppTime().NowUTC(), r.Account().ID)

	return result.CheckEmpty(r.TenantDatabase().Save(&setting).Error)
}

func Upsert(r *shared.AppContext, s SettingsDTO) result.Result[uuid.UUID] {
	var setting Settings

	txErr := r.TenantDatabase().Transaction(func(tx *gorm.DB) error {
		txGet := tx.Where("lower(name) = lower(?)", s.Name)
		if s.Sites != nil {
			txGet = txGet.Where(pgxx.ArrayHasAny("sites", s.Sites))
		} else {
			txGet = txGet.Where("sites IS NULL")
		}

		if err := txGet.First(&setting).Error; err != nil {
			if !errors.Is(err, gorm.ErrRecordNotFound) {
				return err
			}
		}

		var err error
		if setting.Data, err = preprocessSettingByType(tx, s.Data, s.Type); err != nil {
			return err
		}
		setting.Name = s.Name
		setting.Description = s.Description
		setting.Type = s.Type
		setting.Sites = s.Sites
		setting.Public = s.Public
		setting.Active = true

		if err := setting.Validate(); err != nil {
			return err
		}

		if err := ValidateNameUniqueAcrossSites(tx, setting); err != nil {
			return err
		}

		setting.Track(r.AppTime().NowUTC(), r.Account().ID)

		return tx.Save(&setting).Error
	})

	return result.Check(setting.ID, txErr)
}

func Delete(r *shared.AppContext, id uuid.UUID) result.EmptyResult {
	var setting Settings
	if err := r.TenantDatabase().First(&setting, "id = ?", id).Error; err != nil {
		return result.ErrorEmpty(err)
	}

	setting.Active = false
	setting.Name = setting.Name + "_deleted_" + setting.ID.String()

	setting.Track(r.AppTime().NowUTC(), r.Account().ID)

	return result.CheckEmpty(r.TenantDatabase().Save(&setting).Error)
}

func FirstBy(db *gorm.DB, by By) result.Result[Settings] {
	tx := db
	if by.Type != nil {
		tx = tx.Where("type = ?", *by.Type)
	}
	if by.Name != nil {
		tx = tx.Where("name = ?", *by.Name)
	}
	if by.SiteID != nil {
		tx = tx.Where(pgxx.ArrayHasAny("sites", []uuid.UUID{*by.SiteID}))
	}

	tx = tx.Where("active = ?", true)
	tx = tx.Order("created_at DESC")

	var setting Settings
	if err := tx.First(&setting).Error; err != nil {
		return result.Error(err, Settings{})
	}
	return result.Success(setting)
}

func FirstPublicByName(r *shared.AppContext, name string, query string) result.Result[Settings] {
	tx := r.TenantDatabase().
		Where("lower(name) = lower(?)", name).
		Where("public = ?", true).
		Where("active = ?", true).
		Where(r.TenantDatabase().Where("sites IS NULL").Or(pgxx.ArrayHasAny("sites", []uuid.UUID{r.CurrentSiteID()}))).
		Order("sites NULLS FIRST")

	var setting Settings
	if err := tx.First(&setting).Error; err != nil {
		return result.Error(err, Settings{})
	}
	if len(query) > 0 {
		filteredData, err := QueryJSON(setting.Data, query)
		if err != nil {
			return result.Error(err, Settings{})
		}
		setting.Data = filteredData
	}

	return result.Success(setting)
}

func ValidateNameUniqueAcrossSites(db *gorm.DB, setting Settings) error {
	var rows []Settings
	query := db.Table("settings").
		Select("id, name, sites").
		Where("active").
		Where("name = ?", setting.Name)

	if setting.Sites != nil {
		// if sites is null, we need to treat it as a global record
		// meaning that we don't actually have to check for is null or overlap, it's looking for ANY record.
		//query = query.Where("sites IS NULL")
		query = query.Where(pgxx.ArrayHasAny("sites", setting.Sites.Get()))
	}
	if setting.ID != uuid.Nil {
		query = query.Where("id != ?", setting.ID)
	}
	if err := query.Find(&rows).Error; err != nil {
		return err
	}
	if len(rows) == 0 {
		return nil
	}
	conflicts := slicexx.Select(rows, func(s Settings) string {
		return s.Name + "-" + s.ID.String()
	})
	return errors.New(fmt.Sprintf("Name is already taken. Conflicting settings title(s): %s", slicexx.JoinAsStrings(conflicts, ", ")))

}

func preprocessSettingByType(db *gorm.DB, data []byte, settingsType SettingsType) (json.RawMessage, error) {
	switch settingsType {
	case OutlookSecrets, AzureDirectorySecrets:
		appConfig := config.GetAppConfig()
		return msgraph.ValidateClientSecrets(appConfig.MSOAuthApplicationID, appConfig.MSOAuthClientSecret, data)
	case EdsbySecrets:
		return edsby_api.ValidateClientSecrets(data)
	case GoogleCalendarSecrets, GoogleWorkspaceSecrets:
		appConfig := config.GetAppConfig()
		return googleapi.ValidateClientSecrets(appConfig.GCloudIdentityCertificate, data)
	case FacebookSecrets:
		return facebook_api.ValidateClientSecrets(data)
	case GoogleCalendar:
		return google_calendar_api.ValidateCalendarSettings(data)
	case Facebook:
		return facebook_api.ValidatePageSettings(data)
	case Outlook:
		return ValidateOutlookSettings(db, data)
	case Edsby:
		return ValidateEdsbySettings(db, data)
	case ICS:
		return ValidateICSSettings(db, data)
	}

	return data, nil
}

var _ commonModels.IContent = (*Settings)(nil)
var _ commonModels.IEntity = (*Settings)(nil)
var _ commonModels.IContent = (*SettingsDTO)(nil)

//var _ ISettings = (*Settings)(nil)
//var _ ISettings = (*SettingsDTO)(nil)

func (s Settings) GetDepartmentID() *uuid.UUID {
	return nil
}
func (s SettingsDTO) GetDepartmentID() *uuid.UUID {
	return nil
}

func (s Settings) GetID() uuid.UUID {
	return s.ID
}

func (s Settings) GetSites() []uuid.UUID {
	return s.Sites
}
func (s SettingsDTO) GetSites() []uuid.UUID {
	return s.Sites
}

func (s Settings) GetScopeEntity() string {
	return "cm.settings." + string(s.Type)
}

func (s Settings) GetType() string {
	if string(s.Type) == permissions.SettingsInstagram {
		return permissions.SettingsInstagram
	}
	return permissions.SiteSettings
}

func (s SettingsDTO) GetScopeEntity() string {
	return "cm.settings." + string(s.Type)
}

func (s SettingsDTO) GetType() string {
	if string(s.Type) == permissions.SettingsInstagram {
		return permissions.SettingsInstagram
	}
	return permissions.SiteSettings
}

//func (s Settings) GetData() json.RawMessage {
//	return s.Data
//}
//func (s SettingsDTO) GetData() json.RawMessage {
//	return s.Data
//}
