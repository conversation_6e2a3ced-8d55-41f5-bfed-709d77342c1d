## Reservations

The reservation system manages editing sessions to prevent conflicts when multiple users attempt to edit the same content simultaneously.

### Session Key Management

After moving session key management to the client, we lost the "auto-renewal" feature.
If we need it back, we can re-introduce it: The `SessionTimeout` component can export a `reset` function that can be called on Save or on something similar.

### Editing Sessions

The session key is maintained in the browser's sessionStorage. It is renewed every 10 minutes and sent to the server with every request using an axios interceptor.

For `beforeunload` and `navigator.sendBeacon` we use the same key, but send it as a query parameter:
```typescript
navigator.sendBeacon(
        `${RESERVABLE_API}/session/end?${SESSION_KEY}=${sessionStorage.getItem(SESSION_KEY)}`,
        JSON.stringify(data)
)
```

When a user starts an editing session, the current session key is used (even if it is about to expire), but the editing session will be extended when the key is renewed.
This session key management approach has benefits and trade-offs.
- Benefits
  - Centralized session-key management
  - Simple logic for starting and ending sessions with a reservation key as a single dependency
  - Single interceptor can handle multiple entities (no per-entity tracking required)

- Trade-offs
  - Possible race condition if the key is renewed at the same time a user starts or ends an editing session, or performs a write operation

### Checking reservations

The reservation check is implemented in [GORM plugin](./gormxx/reservation_plugin.go). To work properly:
- All entities should implement the `ReservationKey() string` method
- It cannot be used with `.Raw()`/`.Updates()`/etc.; reservations must be checked manually in those cases.
- The plugin must be properly registered with GORM.

Using DB trigger is not an option because the check requires access to the request context.

### Extended Locks

- An extended lock is set by a user and can be removed only by the same user **with an active editing session**.
- Admins can atomically remove extended locks for any user; this operation neither requires nor affects an active editing session.
