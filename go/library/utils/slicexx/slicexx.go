package slicexx

import (
	"fmt"
	"math/rand"
	"strings"
)

func Count[T any](slice []T, f func(T) bool) int {
	count := 0
	for _, t := range slice {
		if f(t) {
			count++
		}
	}
	return count
}

func Random[T any](slice []T) T {
	if len(slice) == 0 {
		var zero T
		return zero
	}

	index := rand.Intn(len(slice))
	return slice[index]
}

func FindFirst[T any](slice []T, f func(T) bool) (T, bool) {
	for _, t := range slice {
		if f(t) {
			return t, true
		}
	}
	var zero T
	return zero, false
}

func Chunk[T any](slice []T, chunkSize int) [][]T {
	var chunks [][]T
	for i := 0; i < len(slice); i += chunkSize {
		end := i + chunkSize

		if end > len(slice) {
			end = len(slice)
		}

		chunks = append(chunks, slice[i:end])
	}
	return chunks
}

func Select[T any, R any](slice []T, f func(T) R) []R {
	var rr = []R{}
	for _, t := range slice {
		rr = append(rr, f(t))
	}
	return rr
}

func JoinAsStrings[T any](slice []T, sep string) string {
	var ss []string
	for _, t := range slice {
		ss = append(ss, fmt.Sprintf("%v", t))
	}
	return strings.Join(ss, sep)
}

func SelectMany[T any, R any](slice []T, f func(T) []R) []R {
	rr := []R{}
	for _, t := range slice {
		rr = append(rr, f(t)...)
	}
	return rr
}

func Distinct[T comparable](slice []T) []T {
	l := len(slice)
	if l == 0 {
		return []T{}
	}
	if l == 1 {
		return []T{slice[0]}
	}
	res := []T{}
	m := make(map[T]interface{}, l)
	for _, t := range slice {
		if _, ok := m[t]; ok {
			continue
		}
		m[t] = struct{}{}
		res = append(res, t)
	}
	return res
}

func Remove[T comparable](slice []T, item T) []T {
	l := len(slice)
	if l == 0 {
		return []T{}
	}
	if l == 1 && slice[0] == item {
		return []T{}
	}
	res := []T{}
	for _, t := range slice {
		if t == item {
			continue
		}
		res = append(res, t)
	}
	return res
}

func DistinctBy[T any, C comparable](slice []T, f func(T) C) []T {
	l := len(slice)
	if l == 0 {
		return []T{}
	}
	if l == 1 {
		return []T{slice[0]}
	}
	res := []T{}
	m := make(map[C]interface{}, l)
	for _, t := range slice {
		c := f(t)
		if _, ok := m[c]; ok {
			continue
		}
		m[c] = struct{}{}
		res = append(res, t)
	}
	return res
}

func Filter[T any](entitySlice []T, filter func(entity T) bool) []T {
	if len(entitySlice) == 0 {
		return entitySlice
	}
	valid := 0
	for _, ref := range entitySlice {
		if filter(ref) {
			entitySlice[valid] = ref
			valid++
		}
	}
	return entitySlice[:valid]
}

func Any[T any](slice []T, check func(entity T) bool) bool {
	if len(slice) == 0 {
		return false
	}
	for _, ref := range slice {
		if check(ref) {
			return true
		}
	}
	return false
}

func Find[T any](sl []T, find func(item T) bool) (T, bool) {
	for _, ref := range sl {
		if find(ref) {
			return ref, true
		}
	}
	return map[string]T{}[""], false
}

func Contains[T comparable](slice []T, item T) bool {
	for _, ref := range slice {
		if ref == item {
			return true
		}
	}
	return false
}

func AsMap[K comparable, V any](slice []V, key func(entity V) K) map[K]V {
	if len(slice) == 0 {
		return map[K]V{}
	}
	m := make(map[K]V, len(slice))
	for _, ref := range slice {
		m[key(ref)] = ref
	}
	return m
}

func ToMap[K comparable, V any, M any](slice []V, key func(entity V) K, value func(entity V) M) map[K]M {
	if len(slice) == 0 {
		return map[K]M{}
	}
	m := make(map[K]M, len(slice))
	for _, ref := range slice {
		m[key(ref)] = value(ref)
	}
	return m
}

func HasCommonElement[T comparable](a, b []T) bool {
	set := make(map[T]struct{})

	for _, elem := range a {
		set[elem] = struct{}{}
	}

	for _, elem := range b {
		if _, exists := set[elem]; exists {
			return true
		}
	}

	return false
}
func EqualComparable[T comparable](a, b []T) bool {
	if len(a) != len(b) {
		return false
	}
	var m = map[T]bool{}
	for _, ref := range a {
		m[ref] = true
	}
	for _, ref := range b {
		if _, ok := m[ref]; !ok {
			return false
		}
	}
	return true
}
func Equal[T any](a, b []T, getIdentifier func(t T) string) bool {
	if len(a) != len(b) {
		return false
	}
	var m = map[string]T{}
	for _, ref := range a {
		m[getIdentifier(ref)] = ref
	}
	for _, ref := range b {
		id := getIdentifier(ref)
		if _, ok := m[id]; !ok {
			return false
		}
	}
	return true
}
func ToBatchesOf[T any](source []T, size int) [][]T {
	var slices [][]T
	var j int
	for i := 0; i < len(source); i += size {
		j += size
		if j > len(source) {
			j = len(source)
		}
		slices = append(slices, source[i:j])
	}
	return slices
}
