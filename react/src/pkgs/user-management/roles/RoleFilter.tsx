import { Autocomplete, Checkbox, FormControl, TextField, Typography } from '@mui/material'
import { useRoleQueryMutation } from '../queries'
import { useMemo } from 'react'
import CenteredSpinner from '@/common/components/CenteredSpinner'

interface RoleFilterProps {
    value: string[]
    onChange: (v: string[]) => void
    label?: string
    variant?: 'standard' | 'outlined' | 'filled'
    required?: boolean
    disabled?: boolean
    helperText?: string
}

export function RoleFilter({
    value,
    onChange,
    label = 'Type',
    variant,
    required,
    disabled,
    helperText
}: RoleFilterProps) {
    const { data: roles, getRoleIdToRole } = useRoleQueryMutation()
    const roleIdToRole = getRoleIdToRole()

    const cleanedValues = useMemo(
        () => value.map((id) => roleIdToRole?.[id]).filter((r) => !!r),
        [value, getRoleIdToRole]
    )

    if (!roles || !roleIdToRole) {
        return <CenteredSpinner />
    }

    return (
        <FormControl variant={variant || 'outlined'} required={required} sx={{ width: '100%' }} disabled={disabled}>
            <Autocomplete
                disabled={disabled || !roles?.length}
                sx={{
                    '&.MuiAutocomplete-root .MuiFormControl-root .MuiInputBase-root': {
                        flexWrap: 'nowrap'
                    }
                }}
                aria-required={required}
                multiple
                disableCloseOnSelect
                disablePortal
                options={roles}
                value={cleanedValues}
                onChange={(ev, newValue) => {
                    onChange(newValue.map((r) => r.id))
                }}
                getOptionLabel={(option) => {
                    return option.name
                }}
                renderInput={(params) => (
                    <TextField
                        {...params}
                        label={label || 'Type'}
                        variant={variant || 'outlined'}
                        required={required}
                        helperText={helperText}
                    />
                )}
                renderOption={(props, option, { selected }) => {
                    const { ...optionProps } = props
                    return (
                        <li key={option.id} {...optionProps} style={{ zIndex: 99999 }}>
                            <Checkbox style={{ marginRight: 8 }} checked={selected} size='small' />
                            <Typography textTransform='capitalize'>{option.name}</Typography>
                        </li>
                    )
                }}
            />
        </FormControl>
    )
}

export default RoleFilter
