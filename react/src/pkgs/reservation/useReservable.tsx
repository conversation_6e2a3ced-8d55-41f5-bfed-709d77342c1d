import { useEffect, useRef, useState } from 'react'
import { httpPost } from '@/common/client'
import { useAtom } from 'jotai/index'
import { activeEditingSessions, SESSION_KEY } from '@/pkgs/reservation/useEditingSession'
import { notify } from '@/helpers'
import { SessionError } from '@/pkgs/reservation/SessionError'
import { RESERVABLE_API } from '@/common/constants'
import { useReservableInfo } from '@/pkgs/reservation/useReservableInfo'
import { useAppContext } from '../auth/atoms'

export function useReservable(key: string) {
    const appContext = useAppContext()
    const [loading, setLoading] = useState(false)
    const [value, setValue] = useState<boolean | null>(null)
    const [, setActiveSessions] = useAtom(activeEditingSessions)
    const reservableInfo = useReservableInfo({ reservationKey: key, isActive: value })

    useEffect(() => {
        value === null && start()

        const onBeforeUnload = async (ev: Event) => {
            navigator.sendBeacon(
                `${RESERVABLE_API}/session/end?${SESSION_KEY}=${sessionStorage.getItem(SESSION_KEY)}`,
                JSON.stringify(data)
            )
            return undefined
        }
        window.addEventListener('beforeunload', onBeforeUnload)

        return () => {
            console.log('Cleaning up reservation switch for key:', key, 'value:', valueRef.current)
            end()
            window.removeEventListener('beforeunload', onBeforeUnload)
        }
    }, [])

    useEffect(() => {
        if (loading) return
        if (!reservableInfo.result.data || reservableInfo.result.isLoading) return
        if (!sessionStorage.getItem(SESSION_KEY)) return
        if (!appContext?.identity()?.ID) return
        if (value === null) return

        const newActive =
            reservableInfo.result.data?.CurrentEditor === appContext?.identity()?.ID &&
            Boolean(reservableInfo.result.data.EditingSession) &&
            Boolean(sessionStorage.getItem(SESSION_KEY)) &&
            reservableInfo.result.data.EditingSession?.toISOString() ===
                new Date(sessionStorage.getItem(SESSION_KEY) || '').toISOString()

        if (newActive !== value) setValue(newActive)
    }, [reservableInfo.result.data])

    const valueRef = useRef(value)
    valueRef.current = value // to keep it updated in the closure

    const data = { ReservationKey: key }

    const start = async () => {
        if (Boolean(valueRef.current)) {
            console.warn('Reservation session already started for key:', key)
            return
        }

        setLoading(true)
        try {
            await httpPost(`${RESERVABLE_API}/session/start`, { ReservationKey: key })
            setValue(true)
            setActiveSessions((count) => count + 1)
            await reservableInfo.result.refetch()
        } catch (error) {
            console.error('Failed to start reservation session:', error)
            setValue(false)

            notify(<SessionError error={error} />, 'error')
        } finally {
            setLoading(false)
        }
    }

    const end = async () => {
        if (!Boolean(valueRef.current)) {
            console.warn('Reservation session already ended for key:', key)
            return
        }

        setLoading(true)
        try {
            await httpPost(`${RESERVABLE_API}/session/end`, data)
            setValue(false)
            setActiveSessions((count) => count - 1)
            await reservableInfo.result.refetch()
        } catch (error) {
            console.error('Failed to end reservation session:', error)
        } finally {
            setLoading(false)
        }
    }

    return {
        reservableInfo,
        loading,
        value,
        start,
        end
    }
}
