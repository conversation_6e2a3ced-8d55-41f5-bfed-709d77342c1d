import { useQuery } from '@tanstack/react-query'
import { z } from 'zod'
import { httpPost, httpPut, httpDelete, httpGet } from '../../../common/client'
import { TagAPIV2 } from '../../../common/constants'
import { trackable, paged, PagingQuery, SortingQuery, baseQueryConfig } from '../../../common/react-query'
import { TagDTO, tags } from './types'

export function createTagQuery(newTag: Partial<TagDTO>) {
    return httpPost(TagAPIV2, newTag, z.string())
}

export function updateTagQuery({ id, tag }: { id: string; tag: Partial<TagDTO> }) {
    return httpPut(`${TagAPIV2}/${id}`, tag, z.string())
}

export function deleteTagQuery(id: string) {
    return httpDelete(`${TagAPIV2}/${id}`)
}

export interface TagsQueryParamsV2 extends Paging<PERSON>uery, SortingQuery {
    Search: string
    Types?: string[]
    MatchAnyType?: boolean
    enabled?: boolean
}

export function useTagsQueryV2(q: TagsQueryParamsV2) {
    return useQuery({
        ...baseQueryConfig,
        queryKey: ['tags-query-v2', q],
        queryFn: async () => httpGet(TagAPIV2, q, tags),
        enabled: q.enabled
    })
}
