import {
    alpha,
    Checkbox,
    FormControlLabel,
    FormGroup,
    FormHelperText,
    FormLabel,
    Grid,
    Stack,
    TextField,
    Typography
} from '@mui/material'
import { getTagTypes } from './types'
import { BoxForm } from '@/common/components/BoxForm'
import { colours } from '@/common/colours'
import { TagDTO } from './types'
import { CMCheckbox } from '@/pkgs/auth/components/CMCheckbox'

interface TagFormProps {
    value: TagDTO
    onChange: (tagDto: TagDTO) => void
    errors: { Name: string; Types: string }
    disableTypeField?: boolean
}

export function TagForm({ value, onChange, errors, disableTypeField }: TagFormProps) {
    const tagTypes = getTagTypes()

    return (
        <BoxForm fullWidth>
            <TextField
                variant='outlined'
                error={!!errors.Name}
                value={value.Name}
                onChange={(e) =>
                    onChange({
                        Name: e.target.value?.toLocaleLowerCase(),
                        Types: value.Types
                    })
                }
                helperText={errors.Name ? 'Entry is too short' : ''}
                name='name'
                label='Tag Name'
                margin='dense'
                id='name'
                autoFocus
                fullWidth
            />
            <Grid container>
                <Grid item>
                    <Stack direction='row' alignItems='center' gap='0.8rem'>
                        <FormLabel disabled={disableTypeField} required error={!!errors.Types}>
                            Type:
                        </FormLabel>
                        <FormGroup sx={{ display: 'flex', flexDirection: 'row', gap: '0.8rem' }}>
                            {tagTypes?.map((tagType) => {
                                const checked = value.Types.includes(tagType)
                                return (
                                    <CMCheckbox
                                        key={tagType}
                                        formControlLabelProps={{
                                            disabled: disableTypeField
                                        }}
                                        label={<Typography textTransform='capitalize'>{tagType}</Typography>}
                                        checked={value.Types.includes(tagType)}
                                        onChange={(e) => {
                                            const checked = e.target.checked

                                            if (checked) {
                                                if (value.Types.includes(tagType)) return
                                                const newValue = { ...value }
                                                value.Types.push(tagType)
                                                onChange(newValue)
                                            } else {
                                                const newValue = { ...value }
                                                const index = newValue.Types.findIndex((type) => type == tagType)

                                                if (index < 0) return

                                                newValue.Types.splice(index, 1)
                                                onChange(newValue)
                                            }
                                        }}
                                    />
                                )
                            })}
                        </FormGroup>
                    </Stack>
                    <FormHelperText
                        disabled={disableTypeField}
                        sx={{
                            fontWeight: 450,
                            fontStyle: 'italic',
                            color: colours.topbar
                        }}
                    >
                        Control where this tag can be used. Cannot be modified after tag has been created.
                    </FormHelperText>
                </Grid>
            </Grid>
        </BoxForm>
    )
}
