import React, { useCallback, useEffect, useMemo, useState } from 'react'
import { useTagsQueryV2 } from './queries'
import { Autocomplete, TextField } from '@mui/material'
import { TagType } from './types'
import { ContentLike } from '@/pkgs/content/types'
import { useURLParamMenuItem } from '../menu-builder/useURLParamMenuItem'

type TagsSelectorProps = {
    tagTypes: TagType[] | string[] | ContentLike[]
    selected: string[]
    onChange?: (value: string[]) => void
    disabled?: boolean
    hasError?: boolean
    required?: boolean
    size?: 'small' | 'medium'
    allowedTags?: string[] // ids
    helperText?: string
    min?: number
    max?: number
}

// used for editors, forms, etc..
export const TagsSelector = ({
    tagTypes,
    onChange,
    selected,
    disabled,
    hasError,
    required,
    size = 'medium',
    allowedTags,
    helperText,
    min,
    max
}: TagsSelectorProps) => {
    const [internalError, setInternalError] = useState('')

    const { data: tagsData, isLoading } = useTagsQueryV2({
        Search: '',
        Types: tagTypes,
        pageSize: 1000
    })

    const tagIdToTagNameMap = useMemo(
        () =>
            tagsData?.Rows?.reduce(
                (a, t) => ({
                    ...a,
                    [t.ID]: t.Name
                }),
                {} as Record<string, string>
            ) || null,
        [tagsData?.Rows]
    )

    const tagIdsToServerTags = useCallback(
        (tags: string[]) => {
            if (!tags?.length) return []

            return tags
                ?.map((id) => ({
                    name: tagIdToTagNameMap?.[id] || '',
                    id
                }))
                .filter((t) => !!t.name)
        },
        [tagIdToTagNameMap]
    )
    const serverTags = useMemo(() => {
        if (!!allowedTags?.length) {
            return (
                tagsData?.Rows?.filter((t) => allowedTags.includes(t.ID)).map((tag) => ({
                    name: tag.Name,
                    id: tag.ID
                })) || []
            )
        }

        return tagsData?.Rows?.map((tag) => ({ name: tag.Name, id: tag.ID })) || []
    }, [allowedTags, tagsData])

    const [value, setValue] = useState<{ name: string; id: string }[]>(tagIdsToServerTags(selected))

    function handleOnChange(selected) {
        setValue(selected)
        onChange?.(selected?.map(({ id }) => id))
    }

    useEffect(() => {
        setValue(tagIdsToServerTags(selected))
    }, [selected, tagIdsToServerTags])

    return (
        <Autocomplete
            loading={isLoading}
            style={{ flex: 1 }}
            onChange={(e, v) => {
                if (max && v.length > max) {
                    setInternalError(`You cannot apply more than ${max} tags`)
                    return
                } else if (min && v.length < min) {
                    setInternalError(`You cannot apply fewer than ${min} tags`)
                    return
                }

                if (!!internalError) {
                    setInternalError('')
                }

                handleOnChange(v)
            }}
            multiple
            disabled={disabled || false}
            filterSelectedOptions
            options={serverTags}
            getOptionLabel={(option) => option.name}
            isOptionEqualToValue={(o, v) => o.id === v.id}
            value={value}
            size={size}
            renderInput={(params) => (
                <TextField
                    required={required}
                    error={hasError || !!internalError}
                    {...params}
                    variant='outlined'
                    label='Tags'
                    helperText={helperText || internalError || 'Start typing or select tags from drop down'}
                />
            )}
        />
    )
}
