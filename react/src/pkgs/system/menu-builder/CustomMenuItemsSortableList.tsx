import { Sortables } from '@/pkgs/ordered-lists/Sortables'
import { ICustomMenuItem } from './queries'
import CenteredSpinner from '@/common/components/CenteredSpinner'
import { iconMap } from './CMIconPicker'
import { Stack, Typography } from '@mui/material'
import { colours } from '@/common/colours'
import CMLink from '@/common/CMLink'
import { getFeaturedMenuItemPath } from '@/app/useAppNavigation'
import { MenuLightCell } from '@/pkgs/grid/cells/MenuLightCell'
import { CustomMenuItem } from '@/common/components/custom-context-menu/CustomMenu'
import CreateIcon from '@mui/icons-material/Create'
import DeleteIcon from '@mui/icons-material/Delete'
import { ContentTypeToTitle } from '@/pkgs/content/types'

interface CustomMenuItemSortableListProps {
    items: ICustomMenuItem[]
    onChange: (items: ICustomMenuItem[]) => void
    editItemHandler: (item: ICustomMenuItem) => void
    deleteItemHandler: (item: ICustomMenuItem) => void
}

export function CustomMenuItemsSortableList({
    items,
    onChange,
    editItemHandler,
    deleteItemHandler
}: CustomMenuItemSortableListProps) {
    const menuItems = (item: ICustomMenuItem) => {
        return (onClose: () => void) => {
            return [
                <CustomMenuItem
                    key={'0'}
                    text={'Edit Item'}
                    onClick={() => {
                        editItemHandler(item)
                        onClose()
                    }}
                >
                    <CreateIcon />
                </CustomMenuItem>,
                <CustomMenuItem
                    key={'1'}
                    text={'Delete Item'}
                    onClick={() => {
                        deleteItemHandler(item)
                        onClose()
                    }}
                >
                    <DeleteIcon />
                </CustomMenuItem>
            ]
        }
    }

    if (!items) {
        return <CenteredSpinner />
    }

    return (
        <Sortables
            value={items || []}
            onChange={onChange}
            renderValue={(item) => {
                const IconComponent = iconMap?.[item.IconName || 'CreateIcon'] || 'CreateIcon'

                return (
                    <Stack
                        key={JSON.stringify(item)}
                        direction='row'
                        sx={{ justifyContent: 'space-between', alignItems: 'center' }}
                    >
                        <Stack>
                            <Stack direction='row' gap='0.8rem' sx={{ alignItems: 'center' }}>
                                {!item.Active && (
                                    <Typography variant='subtitle1' sx={{ color: colours.expired }}>
                                        (Inactive)
                                    </Typography>
                                )}
                                {!item.Active ? (
                                    <Stack direction='row'>
                                        <IconComponent />
                                        <Typography variant='h6'>{item.Label}</Typography>
                                    </Stack>
                                ) : (
                                    <CMLink to={getFeaturedMenuItemPath(item.Route)}>
                                        <Stack direction='row'>
                                            <IconComponent />
                                            <Typography variant='h6'>{item.Label}</Typography>
                                        </Stack>
                                    </CMLink>
                                )}
                            </Stack>

                            {item.Description && <Typography>{item.Description}</Typography>}
                            <Typography>
                                Route: <strong>{item.Route}</strong>
                            </Typography>
                            <Typography>
                                Content Type: <strong>{ContentTypeToTitle[item.ContentType]}</strong>
                            </Typography>
                        </Stack>
                        <MenuLightCell itemsFactory={menuItems(item)} />
                    </Stack>
                )
            }}
        />
    )
}
