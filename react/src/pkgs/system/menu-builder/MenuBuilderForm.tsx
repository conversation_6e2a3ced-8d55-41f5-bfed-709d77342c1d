import { BoxForm } from '@/common/components/BoxForm'
import CMTextField from '@/common/components/CMTextField'
import { ContentTypeSelector } from '@/pkgs/content/ContentTypeSelector'
import { Content, ContentType } from '@/pkgs/content/types'
import { TagsSelector } from '../tags/TagsSelector'
import { alpha, Divider, FormHelperText, Stack, Tab } from '@mui/material'
import { StructureFilter } from '@/pkgs/structure/StructureFilter'
import RoleFilter from '@/pkgs/user-management/roles/RoleFilter'
import { RouteEditor } from '@/pkgs/content/editor/components/RouteEditor'
import { TemplateFilter } from './TemplateFilter'
import { useEffect, useMemo, useState } from 'react'
import { useTemplatesQuery } from '@/pkgs/content/editor/queries'
import { _isEqual } from '@/helpers'
import { CMCheckbox } from '@/pkgs/auth/components/CMCheckbox'
import { Tab<PERSON>ontext, TabList, TabPanel } from '@mui/lab'
import { colours } from '@/common/colours'
import { CMIconPicker, IconName } from './CMIconPicker'
import { customMenuItemTypes, ICustomMenuItem } from './queries'

export type MenuBuilderFormData = ICustomMenuItem
export type MenuBuilderFormDataErrors = Partial<MenuBuilderFormData>

interface MenuConfigFormProps {
    formData: MenuBuilderFormData
    formDataErrors: MenuBuilderFormDataErrors
    onSubmitError?: string
    onChange: (menuItem: MenuBuilderFormData) => void
    isEditMode?: boolean
}

export function MenuBuilderForm({
    formData,
    formDataErrors,
    onChange,
    onSubmitError,
    isEditMode
}: MenuConfigFormProps) {
    const [tab, setTab] = useState<'default' | 'more'>('default')

    const { data } = useTemplatesQuery({
        page: 1,
        pageSize: 1000,
        TemplateType: 'all'
    })

    const templateIdToTemplate = data?.Rows?.reduce(
        (a, t) => ({
            ...a,
            [t.ID]: t
        }),
        {} as Record<string, Content>
    )

    const allowedStructures = useMemo(() => {
        if (!formData?.Templates) return []

        return [
            ...new Set(
                formData.Templates.flatMap(
                    (templateId) => templateIdToTemplate?.[templateId].Structures || null
                ).filter((v) => !!v) as string[]
            )
        ]
    }, [formData.Templates, templateIdToTemplate])

    useEffect(() => {
        if (!!formData?.Templates?.length && !!formData.Structures?.length && allowedStructures?.length > 0) {
            const allowedFilteredStructures = formData?.Structures.filter((id) => allowedStructures.includes(id))

            if (_isEqual(allowedFilteredStructures, formData.Structures)) {
                return
            }

            onChange({
                ...formData,
                Structures: allowedFilteredStructures
            })
        }
    }, [formData.Templates, allowedStructures])

    const tagConfigIsDefault = !formData?.Tags?.length && formData?.TagsMinMax[0] == 0 && formData?.TagsMinMax[1] == 999

    return (
        <>
            <TabContext value={tab}>
                <TabList onChange={(e, newValue) => setTab(newValue)}>
                    <Tab label='Default' value='default' />
                    <Tab
                        label={`More ${tagConfigIsDefault ? '' : '*'}`}
                        value='more'
                        sx={{ backgroundColor: tagConfigIsDefault ? undefined : alpha(colours.published, 0.2) }}
                    />
                </TabList>
                <TabPanel value='default'>
                    <BoxForm>
                        <CMTextField
                            required
                            inputProps={{ maxLength: 50 }}
                            label='Label'
                            value={formData.Label}
                            onChange={(ev) => {
                                if (!isEditMode) {
                                    onChange({
                                        ...formData,
                                        Label: ev.target.value,
                                        Route: ev.target.value.replace(/[^-a-zA-Z0-9_]/g, '')
                                    })
                                } else {
                                    onChange({
                                        ...formData,
                                        Label: ev.target.value
                                    })
                                }
                            }}
                            error={!!formDataErrors.Label}
                            helperText={formDataErrors.Label}
                            debounced
                        />
                        <RouteEditor
                            maxLength={50}
                            required
                            contentType={null}
                            value={formData.Route}
                            onChange={(v) => {
                                onChange({
                                    ...formData,
                                    Route: v
                                })
                            }}
                            error={formDataErrors.Route}
                        />
                        <CMTextField
                            label='Description'
                            value={formData.Description}
                            onChange={(ev) => {
                                onChange({
                                    ...formData,
                                    Description: ev.target.value
                                })
                            }}
                            error={!!formDataErrors.Description}
                            helperText={formDataErrors.Description}
                        />
                        <CMIconPicker
                            value={(formData.IconName || 'CreateIcon') as IconName}
                            onChange={(iconName) => {
                                onChange({
                                    ...formData,
                                    IconName: iconName
                                })
                            }}
                        />
                        <Stack direction='row' sx={{ alignItems: 'center', gap: '8px' }}>
                            <ContentTypeSelector
                                required
                                availableTypes={[...customMenuItemTypes]}
                                value={formData.ContentType as ContentType}
                                onChange={(type) => {
                                    onChange({
                                        ...formData,
                                        ContentType: type,
                                        Tags: [],
                                        Templates: null,
                                        Structures: null
                                    })
                                }}
                            />
                        </Stack>
                        <Divider />
                        <RoleFilter
                            label='Roles'
                            helperText='Only users with these roles can see this menu item. Otherwise, this will be visible to all users.'
                            value={formData.Roles || []}
                            onChange={(newRoles) => {
                                onChange({
                                    ...formData,
                                    Roles: newRoles
                                })
                            }}
                        />
                        <Divider />
                        {[ContentType.Page, ContentType.News, ContentType.Event].includes(
                            formData.ContentType as ContentType
                        ) && (
                            <TemplateFilter
                                classifications={[formData.ContentType]}
                                value={formData?.Templates || []}
                                onChange={(templateIDs, templates) => {
                                    const structures = templates.flatMap((t) => t.Structures || [])

                                    onChange({
                                        ...formData,
                                        Templates: templateIDs,
                                        Structures: structures
                                    })
                                }}
                            />
                        )}
                        {/*  The list of available Structures should depend on the selected Templates. */}
                        <StructureFilter
                            allowedStructures={allowedStructures}
                            value={formData?.Structures || []}
                            onChange={(structures) => {
                                onChange({
                                    ...formData,
                                    Structures: structures
                                })
                            }}
                        />
                        <Divider />
                        <CMCheckbox
                            label='Enabled'
                            checked={formData.Active}
                            onChange={(ev) => {
                                onChange({
                                    ...formData,
                                    Active: ev.target.checked
                                })
                            }}
                        />

                        {onSubmitError && <FormHelperText error={true}>{onSubmitError}</FormHelperText>}
                    </BoxForm>
                </TabPanel>
                <TabPanel value={'more'} sx={{ height: '100%' }}>
                    <BoxForm sx={{ height: '100%' }}>
                        <TagsSelector
                            helperText='Select which tags users are able to use'
                            tagTypes={[formData.ContentType]}
                            selected={formData.Tags || []}
                            onChange={(newTags) => {
                                onChange({
                                    ...formData,
                                    Tags: newTags
                                })
                            }}
                        />
                        <Stack>
                            <Stack direction='row'>
                                <CMTextField
                                    InputProps={{
                                        inputProps: {
                                            min: 0,
                                            max: 10
                                        }
                                    }}
                                    label='Min:'
                                    value={formData?.TagsMinMax?.[0] || 0}
                                    onChange={(ev) => {
                                        onChange({
                                            ...formData,
                                            TagsMinMax: [Number(ev.target.value), formData?.TagsMinMax?.[1] || 0]
                                        })
                                    }}
                                />
                                <CMTextField
                                    InputProps={{
                                        inputProps: {
                                            min: 0,
                                            max: 999
                                        }
                                    }}
                                    label='Max:'
                                    value={formData?.TagsMinMax?.[1] || 0}
                                    onChange={(ev) => {
                                        onChange({
                                            ...formData,
                                            TagsMinMax: [formData?.TagsMinMax?.[0] || 0, Number(ev.target.value)]
                                        })
                                    }}
                                />
                            </Stack>
                            <FormHelperText>
                                Minimum and maximum number of tags users can apply to content
                            </FormHelperText>
                        </Stack>
                    </BoxForm>
                </TabPanel>
            </TabContext>
        </>
    )
}
