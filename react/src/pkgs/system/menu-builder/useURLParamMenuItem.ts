import { useParams } from 'react-router-dom'
import { ICustomMenuItem, useMenuItemsQuery } from './queries'

export function useURLParamMenuItem() {
    const params = useParams()
    const configID = params['configID']
    const customDrawerItemRoute = !!configID ? `/${configID}` : ''

    const results = useMenuItemsQuery()
    return {
        ...results,
        data: !!configID
            ? (results?.menuItems?.CustomMenuItems.find((cdi) => cdi.Route == customDrawerItemRoute) as ICustomMenuItem)
            : null
    }
}
