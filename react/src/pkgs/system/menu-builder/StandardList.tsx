import { DrawerItem, useDrawerItems } from '@/app/routes'
import { Box, Button, FormHelperText, Stack, Typography } from '@mui/material'
import { getSettingsDTO, MenuItems, StandardMenuItem, standardMenuItemPaths, useMenuBuilder } from './queries'
import { useMemo, useState } from 'react'
import { BoxForm } from '@/common/components/BoxForm'
import { useMutation } from '@tanstack/react-query'
import { guessErrorMessage } from '@/helpers/guessErrorMessage'
import { StandardListItem } from './StandardListItem'
import { _isEqual, notify } from '@/helpers'
import RoleSelect from '@/pkgs/user-management/roles/RoleSelect'
import { arrayUnique } from '@/helpers/arrayUnique'

function getRoleIdToRoutes(standardMenuItems: StandardMenuItem[]) {
    if (!standardMenuItems) return {}

    const map = {}
    for (const menuItem of standardMenuItems) {
        if (!menuItem.Roles) {
            continue
        }
        for (const roleId of menuItem.Roles) {
            map[roleId] = arrayUnique([...(map?.[roleId] || []), menuItem.Route])
        }
    }

    return map as Record<string, string[]>
}

function getStandardMenuItemsFromRoleIdToRoutes(roleIdToRoutes: Record<string, string[]>) {
    let items: StandardMenuItem[] = []
    for (const roleId in roleIdToRoutes) {
        for (const route of roleIdToRoutes[roleId]) {
            let existingRoute = items.find((item) => item.Route == route)
            if (!!existingRoute) {
                if (!existingRoute?.Roles) existingRoute.Roles = []
                existingRoute.Roles.push(roleId)
                existingRoute.Roles = arrayUnique(existingRoute.Roles)
                items = items.map((item) => (item.Route == route ? existingRoute! : item))
            } else {
                items.push({
                    Route: route,
                    Roles: [roleId]
                })
            }
        }
    }

    return items
}

function getRouteForDrawerItem(drawerItem: DrawerItem | null) {
    if (!drawerItem) return null

    // should not be possible. (A drawer item with no label)
    if (drawerItem.path === null && !drawerItem.customLabel) return null

    if (drawerItem.path === null) {
        return drawerItem.customLabel
    }

    return drawerItem.path
}

export function getStandardDrawerItem(item: DrawerItem, menuItems: MenuItems): StandardDrawerItem | null {
    if (!standardMenuItemPaths.includes(item.path || '')) return null
    if (!item || item.customLabel == 'Featured') return null

    const route = getRouteForDrawerItem(item)
    const roles = menuItems?.StandardMenuItems.find((i) => i.Route == getRouteForDrawerItem(item))?.Roles

    if (!route) {
        return null
    }

    return {
        ...item,
        Route: route,
        Roles: roles
    }
}

export type StandardDrawerItem = StandardMenuItem & DrawerItem

export function StandardList() {
    const menuBuilder = useMenuBuilder()
    const defaultMenuItems = useMemo(
        () => getRoleIdToRoutes(menuBuilder?.menuItems?.StandardMenuItems),
        [menuBuilder?.menuItems?.StandardMenuItems]
    )
    const drawerItems = useDrawerItems()

    const standardDrawerItems = drawerItems
        .map((item) => {
            if (!item) return null
            return getStandardDrawerItem(item, menuBuilder?.menuItems)
        })
        .filter((i): i is StandardDrawerItem => !!i)

    const [selectedRole, setSelectedRole] = useState('')
    // roleId to drawer item paths
    const [hiddenMenuItems, setHiddenMenuItems] = useState(defaultMenuItems)
    const [onSubmitError, setOnSubmitError] = useState('')

    const hasChanges = useMemo(() => {
        return !_isEqual(
            Object.values(hiddenMenuItems).map((arr) => arr.sort()),
            Object.values(defaultMenuItems).map((arr) => arr.sort())
        )
    }, [hiddenMenuItems, menuBuilder?.menuItems?.StandardMenuItems])

    const updateMutation = useMutation({
        mutationFn: (items: StandardMenuItem[]) =>
            menuBuilder.upsertSettings(getSettingsDTO(menuBuilder?.menuItems?.CustomMenuItems || [], items)),
        onSuccess: (data) => {
            menuBuilder.refetch()
            notify('Success! Standard menu items updated', 'success')
        },
        onError: (err) => {
            const errorMsg = guessErrorMessage(err)
            setOnSubmitError(errorMsg)
        }
    })

    return (
        <>
            <Stack direction='row' gap='0.8rem' justifyContent='space-between' mb='1.2rem'>
                <Typography variant='subtitle1'>
                    Configure Standard Menu Items To Be Hidden For Certain Roles:
                </Typography>
            </Stack>
            <BoxForm>
                {/* Children are instead rendered recursively within <StandardListItem /> */}
                <RoleSelect
                    initialValue={selectedRole}
                    onChange={(roleId) => {
                        setSelectedRole(roleId)
                    }}
                />
                <Stack gap='8px'>
                    {selectedRole ? (
                        standardDrawerItems.map((drawerItem, idx) => {
                            const checked = !!hiddenMenuItems?.[selectedRole]?.includes(drawerItem.Route)

                            return (
                                <StandardListItem
                                    key={drawerItem.Route}
                                    checked={checked}
                                    item={drawerItem}
                                    onChange={(checked) => {
                                        // add or remove from hiddenMenuItems?.[selectedRole]
                                        const updatedRoutes: string[] =
                                            checked === true
                                                ? arrayUnique([
                                                      ...(hiddenMenuItems?.[selectedRole] || []),
                                                      drawerItem.Route
                                                  ])
                                                : hiddenMenuItems?.[selectedRole]?.filter(
                                                      (route) => route != drawerItem.Route
                                                  ) || []

                                        setHiddenMenuItems((p) => ({ ...p, [selectedRole]: updatedRoutes }))
                                    }}
                                />
                            )
                        })
                    ) : (
                        <Typography>Begin by selecting a role</Typography>
                    )}
                </Stack>
                <Box className='box-row'>
                    <Button
                        variant='text'
                        color='primary'
                        type='reset'
                        onClick={() => setHiddenMenuItems(defaultMenuItems)}
                    >
                        Reset Changes
                    </Button>
                    <Button
                        disabled={!hasChanges}
                        variant='contained'
                        color='primary'
                        type='submit'
                        onClick={() => {
                            updateMutation.mutate(getStandardMenuItemsFromRoleIdToRoutes(hiddenMenuItems))
                        }}
                    >
                        {!hasChanges ? 'No Changes' : 'Save'}
                    </Button>
                    {onSubmitError && (
                        <FormHelperText error={true}>
                            <Typography>{onSubmitError}</Typography>
                        </FormHelperText>
                    )}
                </Box>
            </BoxForm>
        </>
    )
}
