import { ListItem, ListItemIcon, ListItemText, Stack } from '@mui/material'
import { StandardDrawerItem } from './StandardList'
import { usePagesIndex } from '@/app/routes'
import { CMCheckbox } from '@/pkgs/auth/components/CMCheckbox'

interface StandardListItemProps {
    item: StandardDrawerItem
    onChange: (checked: boolean) => void
    checked: boolean
}

export function StandardListItem({ item, onChange, checked }: StandardListItemProps) {
    const pagesIndex = usePagesIndex()

    if (item.enabled === false || item.path == null) {
        return null
    }

    return (
        <>
            <ListItem disablePadding>
                <CMCheckbox
                    formControlLabelProps={{
                        sx: {
                            justifyContent: 'space-between'
                        },
                        labelPlacement: 'start'
                    }}
                    label={
                        <Stack
                            direction='row'
                            sx={{ justifyContent: 'center', alignItems: 'center', paddingLeft: '8px' }}
                        >
                            <ListItemIcon>{item.icon?.()}</ListItemIcon>
                            <ListItemText primary={item?.customLabel || pagesIndex[item.path]?.title || ''} />
                        </Stack>
                    }
                    checked={checked}
                    onChange={(e) => {
                        const checked = e.target.checked
                        onChange(checked)
                    }}
                />
            </ListItem>
        </>
    )
}
