import { useIdentity } from '@/pkgs/auth/atoms'
import { z } from 'zod'
import { useSettingsByTypeQuery } from '../settings/queries'
import { Settings, SettingsDTO, SettingsType } from '../settings/types'
import { ContentType } from '@/pkgs/content/types'
import { httpPut } from '@/common/client'
import { SettingsAPI } from '@/common/constants'

// standard menu item route value is retrieved from useDrawerItems hook in routes.tsx
const standardMenuItem = z.object({
    Route: z.string(), // DrawerItem.path
    Roles: z.array(z.string()).nullish() // roles that cannot see this
})

const customMenuItem = z.object({
    ID: z.string(),
    // "/exampleRoute"
    // Is unique
    Route: z.string(),
    Label: z.string(),
    IconName: z.string().nullish(),
    Description: z.string().nullish(),
    ContentType: z.string(),
    Templates: z.array(z.string()).nullish(),
    Structures: z.array(z.string()).nullish(),
    Roles: z.array(z.string()).nullish(), // roles that can see this
    Tags: z.array(z.string()).nullish(),
    TagsMinMax: z.array(z.number()),
    Active: z.boolean()
})

const menuItems = z.object({
    StandardMenuItems: z.array(standardMenuItem),
    CustomMenuItems: z.array(customMenuItem)
})

export type MenuItems = z.infer<typeof menuItems>
export type ICustomMenuItem = z.infer<typeof customMenuItem>
export type StandardMenuItem = z.infer<typeof standardMenuItem>
export const customMenuItemTypes = [
    ContentType.Page,
    ContentType.News,
    ContentType.Event,
    ContentType.Fragment
] as const
export type CustomMenuItemType = (typeof customMenuItemTypes)[number]
export const standardMenuItemPaths = ['/pages', '/news', '/events', '/fragments']

export function getSettingsDTO(
    customMenuItems?: ICustomMenuItem[],
    standardMenuItems?: StandardMenuItem[]
): SettingsDTO {
    return {
        Name: SettingsType.MenuItems,
        Description: '',
        Type: SettingsType.MenuItems,
        Sites: null,
        Data: {
            CustomMenuItems: customMenuItems || [],
            StandardMenuItems: standardMenuItems || []
        }
    }
}

// to be used in menu builder only
export function useMenuBuilder() {
    const result = useMenuItemsQuery()

    return {
        ...result,
        upsertSettings: (settings: SettingsDTO) => httpPut(`${SettingsAPI}`, settings)
    }
}

// returns settings record as "data", and filtered menu items by role ids as "filteredData"
export function useMenuItemsQuery() {
    const identity = useIdentity()
    const userRoleIDs = identity?.Groups?.map((grp) => grp.Role?.id)

    // use name..
    const results = useSettingsByTypeQuery([SettingsType.MenuItems], true)
    const firstItem: Settings | undefined = results?.data?.Rows?.[0]

    // there should only be one record
    const filteredMenuItems = firstItem?.Data as MenuItems

    if (identity.IsAdmin) {
        return {
            ...results,
            data: firstItem,
            menuItems: filteredMenuItems,
            standardMenuItemsToHide: [] as string[]
        }
    }

    if (filteredMenuItems?.CustomMenuItems) {
        filteredMenuItems.CustomMenuItems =
            filteredMenuItems?.CustomMenuItems.filter((menuItem) => {
                if (!menuItem.Roles?.length) return true

                const allowed: boolean = !!menuItem?.Roles?.length
                    ? !!userRoleIDs.filter((id) => menuItem?.Roles!.includes(id)).length
                    : true
                return allowed
            }) || []
    }

    const standardMenuItemsToHide = standardMenuItemPaths.filter((path) => {
        if (!filteredMenuItems?.StandardMenuItems?.length) return false
        const route = filteredMenuItems.StandardMenuItems.find((item) => item.Route == path)

        if (!route?.Roles?.length) return false

        if (userRoleIDs.filter((id) => route.Roles!.includes(id)).length) {
            return true
        }
    })

    return {
        ...results,
        data: firstItem,
        menuItems: filteredMenuItems,
        standardMenuItemsToHide
    }
}
