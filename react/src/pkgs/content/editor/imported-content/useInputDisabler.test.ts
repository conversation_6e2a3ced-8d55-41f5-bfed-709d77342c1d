import { describe, expect } from 'vitest'
import { renderHook } from '@testing-library/react'
import { useInputDisabler, useInputDisablerProps } from './useInputDisabler'
import { test_outlookConfig } from './test_data'
import { generateMock } from '@anatine/zod-mock'
import { Settings, settings } from '../../../../pkgs/system/settings/types'
import { globalWrapper } from '@/pkgs/system/site/site-settings/EditSiteSettings.disabled'
import { StandardizedImportConfig } from './useImportedContent'

// TODO =>  useInputDisabler Should do ____ when content.configIds not found in site Configurations

const expectedResults = {
    allDisabled: { published: true, privacyLevel: true, site: true, tags: true },
    allEnabled: { published: false, privacyLevel: false, site: false, tags: false },
    publishedAndPrivacyLevelOK: { published: false, privacyLevel: false, site: true, tags: true },
    siteAndTagsOK: { published: true, privacyLevel: true, site: false, tags: false }
}

function getMockSettings(n: number, withConfig: StandardizedImportConfig) {
    const allSettings: Settings[] = []
    for (let i = 0; i < n; i++) {
        const newSettings = generateMock(settings)
        newSettings.Data = withConfig
        allSettings.push(newSettings)
    }
    return allSettings
}

export function renderHookWithGlobalWrapper(any) {
    return renderHook(any, { wrapper: globalWrapper })
}

describe('useInputDisabler', () => {
    test('Should return no disabled inputs while loading props', () => {
        const incompleteProps: useInputDisablerProps = {
            content: undefined,
            hasPermission: true,
            test_settings: getMockSettings(3, test_outlookConfig[0])
        }

        const { result } = renderHook((props: useInputDisablerProps) => useInputDisabler(props || incompleteProps), {
            wrapper: globalWrapper
        })

        expect(result.current.importInfo).toBeNull()
        expect(result.current.isImported).toBeFalsy()
        expect(typeof result.current.isInputDisabled).toBe('function')

        const inputState = test_getInputState(result.current.isInputDisabled)
        const expectedResult = expectedResults.allDisabled // content is undefined
        expect(inputState).toMatchObject(expectedResult)
    })

    test('Should return disabled inputs when hasPermission is false and enabled when true', () => {
        const props: useInputDisablerProps = {
            content: {
                Type: 'news',
                Active: true,
                Settings: {
                    imported: true,
                    importInfo: {
                        source: 'outlook',
                        externalId: 'publishedAndPrivacyLevelEnabled'
                    }
                }
            },
            hasPermission: false,
            test_settings: getMockSettings(2, test_outlookConfig[0])
        }
        const { result, rerender } = renderHook((p: useInputDisablerProps) => useInputDisabler(p || props), {
            wrapper: globalWrapper
        })
        const inputState = test_getInputState(result.current.isInputDisabled)
        const expectedResult = expectedResults.allDisabled
        expect(inputState).toMatchObject(expectedResult)

        rerender({ ...props, hasPermission: true })
        const reRenderedInputState = test_getInputState(result.current.isInputDisabled)
        const reRenderedExpectedResult = expectedResults.publishedAndPrivacyLevelOK
        expect(reRenderedInputState).toMatchObject(reRenderedExpectedResult)
    })
    test('Should return enabled inputs when hasPermission is true and import config enables it', () => {
        const props: useInputDisablerProps = {
            content: {
                Type: 'news',
                Active: true,
                Settings: {
                    imported: true,
                    importInfo: {
                        source: 'outlook',
                        externalId: 'publishedAndPrivacyLevelEnabled'
                    }
                }
            },
            test_settings: getMockSettings(3, test_outlookConfig[0]),
            hasPermission: true
        }
        const { result, rerender } = renderHook((rProps: useInputDisablerProps) => useInputDisabler(rProps || props), {
            wrapper: globalWrapper
        })
        const inputState = test_getInputState(result.current.isInputDisabled)
        const expectedResult = expectedResults.publishedAndPrivacyLevelOK
        expect(inputState).toMatchObject(expectedResult)

        rerender({
            ...props,
            content: {
                Type: 'event',
                Active: true,
                Settings: {
                    imported: true,
                    importInfo: {
                        source: 'outlook',
                        externalId: 'siteAndTagsEnabled'
                    }
                }
            },
            test_settings: getMockSettings(3, test_outlookConfig[1])
        })

        const reRenderedInputState = test_getInputState(result.current.isInputDisabled)
        const reRenderedExpectedResult = expectedResults.siteAndTagsOK
        expect(reRenderedInputState).toMatchObject(reRenderedExpectedResult)
    })

    test('Should enable all fields when using non-imported content', () => {
        const props: useInputDisablerProps = {
            content: {
                Type: 'event',
                Active: true,
                Settings: { isAllDay: true, startdate: '2024-04-04', enddate: '2024-04-04' }
            },
            test_settings: getMockSettings(3, test_outlookConfig[0]),
            hasPermission: true
        }
        const { result, rerender } = renderHook((rProps: useInputDisablerProps) => useInputDisabler(rProps || props), {
            wrapper: globalWrapper
        })
        const inputState = test_getInputState(result.current.isInputDisabled)
        const expectedResult = expectedResults.allEnabled

        expect(inputState).toMatchObject(expectedResult)

        rerender({
            ...props,
            content: {
                Type: 'event',
                Active: true,
                Settings: {}
            }
        })
        const reRenderedInputState = test_getInputState(result.current.isInputDisabled)
        expect(reRenderedInputState).toMatchObject(expectedResult)
    })

    test('Should disable any non-import related fields', () => {
        const props: useInputDisablerProps = {
            content: {
                Type: 'news',
                Active: true,
                Settings: {
                    imported: true,
                    importInfo: {
                        source: 'outlook',
                        externalId: 'publishedAndPrivacyLevelEnabled'
                    }
                }
            },
            test_settings: getMockSettings(3, test_outlookConfig[0]),
            hasPermission: true
        }
        const { result } = renderHook((p: useInputDisablerProps) => useInputDisabler(p || props), {
            wrapper: globalWrapper
        })
        const inputState = {
            title: result.current.isInputDisabled('title'),
            route: result.current.isInputDisabled('route'),
            settings: result.current.isInputDisabled('settings'),
            meta: result.current.isInputDisabled('meta'),
            startdate: result.current.isInputDisabled('startdate'),
            test_random: result.current.isInputDisabled('test_random')
        }
        expect(inputState).toMatchObject({
            title: true,
            route: true,
            settings: true,
            meta: true,
            startdate: true,
            test_random: true
        })
    })

    test('Should be fully disabled if content is imported and no editable fields are found', () => {
        const props: useInputDisablerProps = {
            content: {
                Type: 'event',
                Active: true,
                Settings: {
                    imported: true,
                    importInfo: {
                        source: 'edsby',
                        externalId: 'fullyDisabled'
                    }
                }
            },
            test_settings: getMockSettings(3, test_outlookConfig[0]),
            hasPermission: true
        }
        const { result } = renderHook((p: useInputDisablerProps) => useInputDisabler(p || props), {
            wrapper: globalWrapper
        })
        expect(result.current.isFullyDisabled).toBe(true)
        const inputState = {
            tags: result.current.isInputDisabled('tags'),
            meta: result.current.isInputDisabled('meta')
        }
        expect(inputState).toMatchObject({
            tags: true,
            meta: true
        })
    })
})

function test_getInputState(isInputDisabledFunc: (name: string) => boolean) {
    return {
        published: isInputDisabledFunc('published'),
        privacyLevel: isInputDisabledFunc('privacyLevel'),
        site: isInputDisabledFunc('site'),
        tags: isInputDisabledFunc('tags')
    }
}
