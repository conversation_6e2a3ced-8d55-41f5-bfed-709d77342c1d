import React, { useContext, useEffect, useState } from 'react'
import { disabledContext } from '../../../common/DisabledContext'
import PageManager from './PageManager'
import { AddButton } from '../../../common/components'
import { useLocation } from 'react-router-dom'
import { useAppContext } from '../../auth/atoms'
import { EntityScopeEnum } from '../../auth/entityScope'
import PageContainerWithHeader from '../../../common/components/PageContainerWithHeader'
import { CreateStructuredContent } from '../editor/CreateStructuredContent'
import './Pages.css'
import { ContentType } from '../types'

const Pages = () => {
    const evaluators = useAppContext()
    const [, setDisabled] = useContext(disabledContext)
    const [newPage, setNewPage] = useState(false)

    // Enable Site Select
    useEffect(() => {
        //@ts-ignore
        setDisabled(false)
    }, [])

    return (
        <PageContainerWithHeader
            title='Pages'
            topRightElement={
                evaluators.actionForEntityScope(EntityScopeEnum.Page, 'create') ? (
                    <AddButton title='ADD PAGE' func={() => setNewPage(true)} style={{ marginRight: 10 }} />
                ) : null
            }
        >
            <PageManager />
            {newPage && (
                <CreateStructuredContent
                    availableTypes={[ContentType.Page]}
                    open={newPage}
                    onClose={() => setNewPage(false)}
                />
            )}
        </PageContainerWithHeader>
    )
}

export default Pages
