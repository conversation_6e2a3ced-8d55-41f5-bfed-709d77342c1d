import { useStateWithStorage } from '@/common/storage.service'
import { Content, ContentType } from '../types'
import AppAccordion from '@/common/components/AppAccordion'
import { Box, Tab, Typography } from '@mui/material'
import { mainBorderRadius } from '@/app/theme'
import { Tab<PERSON>ontext, Tab<PERSON>ist, TabPanel } from '@mui/lab'
import { useAppContext } from '@/pkgs/auth/atoms'
import { PinnedNews } from './PinnedNews'
import { ListsForGrids } from '@/pkgs/ordered-lists/distributed-lists/ListsForGrids'

interface FeaturedNewsAccordionProps {
    setMenuElement: (ev: HTMLButtonElement, state: any) => void
    menuItems: (item: Content) => (onClose: () => void) => (false | JSX.Element)[]
}
export function FeaturedNewsAccordion({ setMenuElement }: FeaturedNewsAccordionProps) {
    // TODO: Remove setMenuElement, become self sufficient

    const evaluators = useAppContext()
    const [currentTab, setCurrentTab] = useStateWithStorage(`featured-tab-selector-${ContentType.News}`, 'pinned')
    const [isExpanded, setIsExpanded] = useStateWithStorage(`featured-expanded-${ContentType.News}`, false)
    return (
        <AppAccordion
            withoutPadding
            expanded={isExpanded}
            onChangeHandler={(expanded) => {
                setIsExpanded(expanded)
            }}
            summary={
                <Typography variant='h6' sx={{ borderRadius: mainBorderRadius }}>
                    Featured
                </Typography>
            }
            details={
                <TabContext value={currentTab}>
                    <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
                        <TabList onChange={(e, newValue) => setCurrentTab(newValue)}>
                            <Tab label={'Pinned News'} value={'pinned'} sx={{ fontSize: '16px' }} />
                            {evaluators.getConfig('EnableFeaturedOrderedLists') && (
                                <Tab label={'Ordered Lists'} value={'lists'} sx={{ fontSize: '16px' }} />
                            )}
                        </TabList>
                    </Box>
                    <TabPanel value={'pinned'}>
                        <PinnedNews setMenuElement={setMenuElement} />
                    </TabPanel>
                    <TabPanel value={'lists'}>
                        <ListsForGrids contentType={ContentType.News} />
                    </TabPanel>
                </TabContext>
            }
        />
    )
}
