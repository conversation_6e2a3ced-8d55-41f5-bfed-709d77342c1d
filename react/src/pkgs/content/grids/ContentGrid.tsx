import { Content, ContentLike, ContentType, ContentTypeToTitle } from '../types'
import { useAppContext, useCurrentSiteID } from '@/pkgs/auth/atoms'
import { useMemo, useState } from 'react'
import { ContentQueryParams, useContentQuery } from '../queries'
import { defaultPageQuery } from '@/common/react-query'
import { useURLParamMenuItem } from '@/pkgs/system/menu-builder/useURLParamMenuItem'
import PageContainerWithHeader from '@/common/components/PageContainerWithHeader'
import { Button, FormControlLabel, Grid, Icon, Menu, Stack, Switch } from '@mui/material'
import SearchBar from '@/common/components/SearchBar'
import { TagsSelector } from '@/pkgs/system/tags/TagsSelector'
import { EditorsSelectorV2 } from '@/pkgs/grid/selectors/EditorsSelectorV2'
import { DepartmentsAutocompleteV2 } from '@/pkgs/grid/selectors/DepartmentsAutocompleteV2'
import { StatusSelector } from '../BaseForm'
import { GridColDef } from '@mui/x-data-grid'
import {
    DateCell,
    EditorsCellV2,
    MenuLightCell,
    PrivacyLevelCell,
    PublishPeriodCell,
    TemplateCell,
    TitleCell,
    TwoLinesCell
} from '@/pkgs/grid/cells/GridCells'
import { getContentEditorPath, getFeaturedMenuItemContentEditorPath, useAppNavigation } from '@/app/useAppNavigation'
import { DataGridBase } from '@/pkgs/grid/DataGridBase'
import { CustomMenuItem } from '@/common/components/custom-context-menu/CustomMenu'
import { PreviewMenuItem } from '@/common/components/selectors/SiteSelectorForContent'
import { asSecured } from '@/pkgs/auth/permissions/securityMapping'
import CreateIcon from '@mui/icons-material/Create'
import ReadMoreIcon from '@mui/icons-material/ReadMore'
import { DeleteCustomMenuItem } from '@/common/components/custom-context-menu/ContentDeleteMenuItem'
import { CloneCustomMenuItem } from '@/common/components/custom-context-menu/ContentCloneMenuItem'
import { notify } from '@/helpers'
import { TransferDepartmentMenuItem } from '@/common/components/custom-context-menu/ContentTransferDepartmentMenuItem'
import { contentTypes, fromOptional } from '@/common/components/selectors/SiteSelectorForAccount'
import { TemplateFilter } from '@/pkgs/system/menu-builder/TemplateFilter'
import { CreateStructuredContent } from '../editor/CreateStructuredContent'
import AddButton from '@/common/components/AddButton'
import { EntityScopeEnum, typeToEntityScope } from '@/pkgs/auth/entityScope'
import { PinnedToggleCustomMenuItem } from '@/common/components/custom-context-menu/ContentPinnedToggleMenuItem'
import { ExpireToggleCustomMenuItem } from '@/common/components/custom-context-menu/ContentExpireToggleMenuItem'
interface ContentGridProps extends Pick<ContentQueryParams, 'ContentTypes'> {}

export function ContentGrid({ ContentTypes }: ContentGridProps) {
    const { data: cdi } = useURLParamMenuItem()

    const currentSiteID = useCurrentSiteID()

    const defaultQuery: ContentQueryParams = {
        ...defaultPageQuery,
        ContentTypes: ContentTypes,
        Sites: currentSiteID ? [currentSiteID] : [],
        StructureID: cdi?.Structures?.[0],
        Tags: cdi?.Tags || undefined,
        Templates: cdi?.Templates || undefined
    }

    const [query, setQuery] = useState<ContentQueryParams>({ ...defaultQuery })
    const results = useContentQuery(query)
    const appNavigation = useAppNavigation()
    const evaluators = useAppContext()

    const [createContentDialogIsOpen, setCreateContentDialogIsOpen] = useState(false)

    const menuItems = (item: Content) => {
        const secured = asSecured(item)
        const ctxItemPermission = useMemo(
            () => ({
                update: item == null ? false : evaluators.action(item, 'update') || item?.Settings?.isDistrictPage,
                create: item == null ? false : evaluators.action(item, 'create'),
                delete: item == null ? false : evaluators.action(item, 'delete')
            }),
            [item]
        )

        const editorPath = cdi?.Route
            ? getFeaturedMenuItemContentEditorPath(cdi?.Route || '', item.ID)
            : getContentEditorPath(item)

        // pin and expire/unexpire
        return (onClose: () => void) => {
            return [
                <PreviewMenuItem
                    key='Preview'
                    content={{
                        id: item.ID,
                        type: item.Type,
                        sites: item.Sites,
                        route: item.Route,
                        Workspace: item.Workspace
                    }}
                    onClick={onClose}
                />,
                <CustomMenuItem
                    key='Edit'
                    disabled={!editorPath}
                    text={ctxItemPermission.update ? 'Edit' : 'View'}
                    onClick={() => (!!editorPath ? appNavigation.navigateTo(editorPath) : {})}
                >
                    {ctxItemPermission.update ? <CreateIcon /> : <ReadMoreIcon />}
                </CustomMenuItem>,
                <CloneCustomMenuItem
                    key='Clone'
                    disabled={!ctxItemPermission.create}
                    id={item?.ID}
                    onChange={(newId) => {
                        notify(`Succesfully cloned page: ${item.Title}`, 'success')
                        onClose()

                        if (!newId.data) {
                            return
                        }

                        const clonedContentPath = cdi?.Route
                            ? getFeaturedMenuItemContentEditorPath(cdi?.Route, newId.data)
                            : getContentEditorPath(newId.data)

                        if (clonedContentPath) {
                            appNavigation.navigateTo(clonedContentPath)
                        }
                    }}
                    workspace={item.Workspace}
                />,
                ...([ContentType.News, ContentType.Event].includes(item.Type as ContentType)
                    ? [
                          // TODO: Pinned news UI needs to be refactored before we allow user to pin/unpin in this UI
                          //   <PinnedToggleCustomMenuItem
                          //       disabled={!ctxItemPermission.update}
                          //       key='pin-toggle'
                          //       value={item}
                          //       onChange={() => {
                          //           onClose()
                          //           results?.refetch()
                          //       }}
                          //   />,
                          <ExpireToggleCustomMenuItem
                              key='expire-toggle'
                              disabled={!ctxItemPermission.update}
                              value={item}
                              onChange={() => {
                                  notify(`Success, the expiry date for ${item?.Title || ''} has been updated`, 'info')
                                  onClose()
                                  results.refetch()
                              }}
                          />
                      ]
                    : []),

                <DeleteCustomMenuItem
                    key='Delete'
                    disabled={!ctxItemPermission.delete}
                    contentModel={item}
                    onChange={() => {
                        results.refetch()
                    }}
                    // TODO: closing the menu will also close the confirm dialog
                    closeMenu={() => {}}
                />,
                evaluators.isAdmin() && (
                    <TransferDepartmentMenuItem
                        key='Department'
                        contentModel={{
                            id: item?.ID,
                            departmentId: fromOptional(item?.DepartmentID),
                            sites: item?.Sites || [],
                            contentType: item.Type as contentTypes,
                            settings: item?.Settings
                        }}
                        onChange={() => {
                            results.refetch()
                        }}
                        // TODO: closing the menu will also close the confirm dialog
                        closeMenu={() => {}}
                    />
                )
            ]
        }
    }

    const columns: GridColDef[] = [
        {
            // TODO: add navigation tree for admin content v2 api
            field: 'Title',
            headerName: 'Title',
            flex: 2,
            filterable: false,
            sortable: true,
            renderCell: (cellParams) => <TitleCell {...cellParams} />
        },
        {
            field: 'Editor',
            headerName: 'Author/Editor',
            width: 150,
            filterable: false,
            sortable: false,
            renderCell: (cellParams) => <EditorsCellV2 {...cellParams} />
        },
        {
            field: 'PublishPeriod',
            headerName: 'Status',
            width: 250,
            sortable: false,
            filterable: false,
            renderCell: (cellParams) => (
                <PublishPeriodCell publishAt={cellParams.row.PublishAt} expireAt={cellParams.row.ExpireAt} />
            )
        },
        {
            headerName: 'Date',
            field: 'Updated',
            filterable: true,
            sortable: true,
            type: 'dateCustom',
            disableColumnMenu: false,
            flex: 1,
            renderCell: (cellParams) => <DateCell {...cellParams} />
        },
        // TODO: add "template title" field to admin content v2 api or just query all templates and use content.path
        // {
        //     headerName: 'Template',
        //     field: 'Template',
        //     filterable: true,
        //     sortable: false,
        //     type: 'containsText',
        //     disableColumnMenu: false,
        //     flex: 1,
        //     renderCell: (cellParams) => <TemplateCell {...cellParams} />
        // },
        {
            headerName: 'Privacy Level',
            field: 'PrivacyLevel',
            filterable: true,
            sortable: false,
            disableColumnMenu: false,
            flex: 1,
            align: 'center',
            type: 'singleSelect',
            headerAlign: 'center',
            valueOptions: [
                'public',
                'staff'
                // 'undefined',
            ],
            renderCell: (cellParams) => <PrivacyLevelCell {...cellParams} />
        },
        {
            headerName: 'Actions',
            field: 'menu',
            filterable: false,
            sortable: false,
            disableColumnMenu: true,
            flex: 1,
            renderCell: (cellParams) => <MenuLightCell itemsFactory={menuItems(cellParams.row)} />,
            headerAlign: 'center',
            align: 'center'
        }
    ]

    return (
        <PageContainerWithHeader
            title={cdi?.Label || ContentTypeToTitle?.[cdi?.ContentType || ''] || 'Content'}
            topRightElement={
                evaluators.actionForEntityScope(
                    typeToEntityScope(cdi?.ContentType || ContentTypes?.[0] || ''),
                    'create'
                ) ? (
                    <AddButton
                        title='Create'
                        func={() => setCreateContentDialogIsOpen(true)}
                        style={{ marginRight: 10 }}
                    />
                ) : null
            }
        >
            <Grid container spacing={2}>
                <Grid item md={3}>
                    <SearchBar
                        value={query.Search || ''}
                        onChange={(val) => {
                            setQuery((p) => ({
                                ...p,
                                Search: val
                            }))
                        }}
                    />
                </Grid>
                <Grid item md={3}>
                    <TagsSelector
                        helperText='Select which tags users are able to use'
                        allowedTags={cdi?.Tags || undefined}
                        tagTypes={ContentTypes!}
                        selected={query.Tags || []}
                        onChange={(newTags) => {
                            setQuery((p) => ({
                                ...p,
                                Tags: newTags
                            }))
                        }}
                    />
                </Grid>
                <Grid item md={3}>
                    <EditorsSelectorV2
                        value={query.Editors || []}
                        onChange={(val) => {
                            setQuery((p) => ({
                                ...p,
                                Editors: val
                            }))
                        }}
                    />
                </Grid>
                <Grid item md={3}>
                    <DepartmentsAutocompleteV2
                        value={query.Departments || []}
                        onChange={(val) => {
                            setQuery((p) => ({
                                ...p,
                                Departments: val
                            }))
                        }}
                    />
                </Grid>
                <Grid item md={3}>
                    <StatusSelector
                        value={query.Status || ''}
                        onChange={(v) => {
                            setQuery((p) => ({
                                ...p,
                                Status: v
                            }))
                        }}
                    />
                </Grid>
                <Grid item md={3}>
                    <TemplateFilter
                        value={query.Templates || []}
                        onChange={(v) => {
                            setQuery((p) => ({
                                ...p,
                                Templates: v
                            }))
                        }}
                        classifications={query.ContentTypes}
                        allowedTemplateIDs={cdi?.Templates || undefined}
                    />
                </Grid>
                <Grid item md={3}>
                    <Stack direction='row' gap='0.8rem'>
                        <FormControlLabel
                            disabled={evaluators.isCurrentSiteDepartment()}
                            value='start'
                            control={
                                <Switch
                                    checked={query.SiteOnly}
                                    onChange={(e) => {
                                        const checked = e.target.checked
                                        setQuery((p) => ({
                                            ...p,
                                            SiteOnly: checked
                                        }))
                                    }}
                                    color='secondary'
                                />
                            }
                            label='Site Only'
                            labelPlacement='start'
                        />
                        <FormControlLabel
                            value='start'
                            control={
                                <Switch
                                    checked={query.Inactive}
                                    onChange={(e) => {
                                        const checked = e.target.checked
                                        setQuery((p) => ({
                                            ...p,
                                            Inactive: checked
                                        }))
                                    }}
                                    color='secondary'
                                />
                            }
                            label='Deleted'
                            labelPlacement='start'
                        />
                    </Stack>
                </Grid>
                <Grid item md={3} sx={{ textAlign: 'right' }}>
                    <Button
                        onClick={() => {
                            setQuery({
                                ...defaultQuery,
                                Inactive: false,
                                SiteOnly: false
                            })
                        }}
                        color={'primary'}
                        style={{ marginLeft: 'auto' }}
                    >
                        Reset Filters
                    </Button>
                </Grid>
                <Grid item md={12}>
                    <DataGridBase
                        // rowHeight={100}
                        columns={columns}
                        state={results.data}
                        setQuery={setQuery}
                    />
                </Grid>
            </Grid>
            {createContentDialogIsOpen && (
                <CreateStructuredContent
                    availableTypes={(cdi?.ContentType ? [cdi.ContentType] : ContentTypes) as ContentLike[]}
                    open={createContentDialogIsOpen}
                    onClose={() => setCreateContentDialogIsOpen(false)}
                />
            )}
        </PageContainerWithHeader>
    )
}
