import React from 'react'
import { FormControlLabel, FormLabel, Radio, RadioGroup } from '@mui/material'
import { ContentType, getAllContentLikeContent } from './types'

interface ContentTypeSelectorProps {
    value: ContentType
    onChange: (contentType: ContentType) => void
    availableTypes?: ContentType[]
    required?: boolean
}

export const ContentTypeSelector = ({ value, onChange, availableTypes, required }: ContentTypeSelectorProps) => {
    const options = availableTypes || getAllContentLikeContent()
    if (!value) {
        value = options[0]
        onChange(value)
    }

    return (
        <>
            <FormLabel required={required}>Content Type:</FormLabel>
            <RadioGroup row>
                {options.map((type) => (
                    <FormControlLabel
                        sx={{ textTransform: 'capitalize' }}
                        key={type}
                        value={type}
                        control={<Radio />}
                        label={type}
                        checked={value === type}
                        onChange={() => onChange(type)}
                    />
                ))}
            </RadioGroup>
        </>
    )
}
