import { useState } from 'react'
import { CustomMenuItem } from './CustomMenu'
import DeleteIcon from '@mui/icons-material/Delete'
import { BaseContextMenuItemProps } from './types'
import { notify, validateUUID } from '../../../helpers'
import ConfirmAction from '../ConfirmAction'
import { deleteContentQuery, getDeleteContentMutation } from '@/pkgs/content/queries'
import { guessErrorMessage } from '@/helpers/guessErrorMessage'
import { useMutation } from '@tanstack/react-query'

export const DeleteCustomMenuItem = ({
    contentModel,
    onChange,
    closeMenu,
    disabled
}: BaseContextMenuItemProps & {
    closeMenu: () => void
    contentModel: Record<string, any>
}) => {
    const contentId = contentModel?.id || contentModel?.ID
    const contentTitle = contentModel?.title || contentModel?.Title
    const workspace = contentModel?.workspace || contentModel?.Workspace
    const [isOpen, setIsOpen] = useState<boolean>(false)

    const deleteContentMutation = useMutation({
        mutationFn: (content: any) => deleteContentQuery(content.ID, content.Workspace),
        onSuccess: (res) => {
            onChange?.()
            setClosed()
            notify(`Successfully deleted ${contentTitle}`, 'success')
        },
        onError: (err) => {
            const msg = guessErrorMessage(err)
            notify(msg, 'error')
        }
    })

    const handleDelete = async () => {
        try {
            if (!validateUUID(contentId)) {
                return Promise.reject({ message: 'invalid id' })
            }
            deleteContentMutation.mutate({ ID: contentId, Workspace: workspace })
        } catch (err) {
            console.error(err)
            return Promise.reject(err)
        }
    }
    const setClosed = () => setIsOpen(false)
    const open = () => {
        closeMenu?.()
        setIsOpen(true)
    }
    return (
        <>
            <CustomMenuItem text={'Delete'} onClick={open} disabled={Boolean(disabled)}>
                <DeleteIcon />
            </CustomMenuItem>
            <ConfirmAction
                open={isOpen}
                title={`Delete ${contentTitle}`}
                text={'Are you sure you want to delete this?'}
                handleClose={setClosed}
                handleDisagree={setClosed}
                handleAgree={handleDelete}
            />
        </>
    )
}
