import LocationOffIcon from '@mui/icons-material/LocationOff'
import LocationOnIcon from '@mui/icons-material/LocationOn'
import React, { useMemo } from 'react'
import { contentService } from '../../../pkgs/content/content.service'
import { BaseContextMenuItemProps } from './types'
import { is, notify } from '../../../helpers'
import { CustomMenuItem } from './CustomMenu'
import { guessErrorMessage } from '../../../helpers/guessErrorMessage'
import { useContentQuery } from '@/pkgs/content/queries'
import { Content } from '@/pkgs/content/types'
import { useMutation } from '@tanstack/react-query'
import { ContentAPIV2 } from '@/common/constants'
import { httpPut } from '@/common/client'

export const PinnedToggleCustomMenuItem = ({
    value,
    onChange,
    disabled
}: BaseContextMenuItemProps & {
    value: Record<string, any>
}) => {
    // TODO: Deprecate admin api v1
    const existingPriority = value?.settings?.priority || value?.Settings?.priority
    const { data } = useContentQuery({
        UsePriority: true,
        pageSize: 9999
    })

    const newPriority = useMemo(() => {
        if (is.number(existingPriority)) {
            return null
        } else {
            return data?.Rows?.pop()?.Settings?.priority + 1 || 999
        }
    }, [data?.Rows])

    // TODO => Permissions => Patch requires Shareable interface

    const contentMutation = useMutation({
        mutationFn: (content: Content) => httpPut(`${ContentAPIV2}/${content.ID}/${content.Workspace}`, content),
        onSuccess: (data) => {
            onChange?.()
        }
    })

    const handlePriorityToggle = async () => {
        // For admin api v1 (CombinedNewsEvents component)
        if (!!value?.title) {
            try {
                const next = { ...value, settings: { ...value?.settings, priority: newPriority } }
                const response = await contentService.patchContent('', next)
                notify(`Success, ${value?.title} has been ${newPriority === null ? 'un-pinned' : 'pinned'}`, 'info')
                onChange?.(response)
            } catch (err) {
                notify(`Error pinning content: ${guessErrorMessage(err)}`, 'error')
                console.error(err)
            }
        } else {
            contentMutation.mutate({
                ...value,
                Settings: {
                    ...value?.Settings,
                    priority: newPriority
                }
            } as Content)
        }
    }

    if (!value) {
        return null
    }

    return (
        <CustomMenuItem
            text={is.number(existingPriority) ? 'Un-pin' : 'Pin'}
            onClick={handlePriorityToggle}
            disabled={Boolean(disabled)}
        >
            {is.number(value?.settings?.priority) ? <LocationOffIcon /> : <LocationOnIcon />}
        </CustomMenuItem>
    )
}
